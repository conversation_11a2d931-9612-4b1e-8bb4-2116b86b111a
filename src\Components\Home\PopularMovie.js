import React, { useState, useEffect } from "react";
import { BsCollectionFill } from "react-icons/bs";
import Titles from "../Titles";
import Movie from "../Movie";
import { movieService } from "../../api/services";
import useAuthErrorHandler from "../../hooks/useAuthErrorHandler";

function PopularMovie() {
  const [movies, setMovies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Auth error handler
  const { executeApiCall } = useAuthErrorHandler(
    "Failed to load popular movies."
  );

  // تحميل الأفلام عند تحميل المكون
  useEffect(() => {
    loadPopularMovies();
  }, []);

  // تحميل الأفلام الشائعة من الـ API
  const loadPopularMovies = async () => {
    setLoading(true);
    try {
      const response = await executeApiCall(() =>
        movieService.getAllMovies({
          orderByRate: true, // ترتيب حسب التقييم
          PageSize: 8, // أول 8 أفلام
        })
      );

      if (response?.data?.success && response.data.data) {
        // تحويل البيانات لتتوافق مع Movie component
        const formattedMovies = response.data.data.map((movie) => ({
          id: movie.id,
          name: movie.name,
          image: movie.image?.url || "default-movie.jpg",
          Category:
            movie.movieTypes?.map((type) => type.englishName).join(", ") ||
            "N/A",
          language:
            movie.movieLanguages?.map((lang) => lang.englishName).join(", ") ||
            "N/A",
          year: movie.year,
          time: movie.durationInMinutes
            ? `${movie.durationInMinutes} min`
            : "N/A",
          rate: movie.rate,
          desc: movie.description,
          titleImage:
            movie.secondaryImage?.url ||
            movie.image?.url ||
            "default-movie.jpg",
        }));

        setMovies(formattedMovies);
      }
    } catch (error) {
      console.error("Error loading popular movies:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="my-16">
      <Titles title="Popular Movies" Icon={BsCollectionFill} />

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-beige3"></div>
          <span className="ml-3 text-white">Loading popular movies...</span>
        </div>
      ) : (
        <div className="grid sm:mt-12 mt-6 xl:grid-cols-4 sm:grid-cols-2 grid-gols-1 gap-10 animate-fadeIn">
          {movies.map((movie, index) => (
            <Movie key={movie.id || index} movie={movie} />
          ))}
        </div>
      )}

      {!loading && movies.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-400">
            No popular movies available at the moment.
          </p>
        </div>
      )}
    </div>
  );
}

export default PopularMovie;
