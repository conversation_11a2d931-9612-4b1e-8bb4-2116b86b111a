[{"E:\\cinema\\Cinemate1\\src\\Screens\\Dashboard\\Admin\\AddMovie.js": "1"}, {"size": 38079, "mtime": 1747697508730, "results": "2", "hashOfConfig": "3"}, {"filePath": "4", "messages": "5", "suppressedMessages": "6", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1fk7kd8", "E:\\cinema\\Cinemate1\\src\\Screens\\Dashboard\\Admin\\AddMovie.js", ["7", "8", "9"], [], {"ruleId": "10", "severity": 1, "message": "11", "line": 61, "column": 20, "nodeType": "12", "messageId": "13", "endLine": 61, "endColumn": 31}, {"ruleId": "14", "severity": 1, "message": "15", "line": 94, "column": 6, "nodeType": "16", "endLine": 94, "endColumn": 39, "suggestions": "17"}, {"ruleId": "10", "severity": 1, "message": "18", "line": 415, "column": 9, "nodeType": "12", "messageId": "13", "endLine": 415, "endColumn": 24}, "no-unused-vars", "'setPageSize' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCastMembers'. Either include it or remove the dependency array.", "ArrayExpression", ["19"], "'openDeleteModal' is assigned a value but never used.", {"desc": "20", "fix": "21"}, "Update the dependencies array to be: [currentPage, pageSize, castType, fetchCastMembers]", {"range": "22", "text": "23"}, [3449, 3482], "[currentPage, pageSize, castType, fetchCastMembers]"]