
export const Enjoy=[
    {
        name:'head snack1',
        image:'enjoy1.png',
    },
    {
        name:'head snack1',
        image:'enjoy.png',
    },
]
export const Varieties=[
    {
        name:'Popcorn',
        image:'Popcorn1.jpg',
    },
    {
        name:'<PERSON><PERSON><PERSON>',
        image:'Nachos.jpeg',
    },
    {
        name:'Drink<PERSON>',
        image:'Pepsi.jpg',
    },
]

export const UsersData = [
    { 
      name: '<PERSON>', 
      image: 'c1.jpg',
      message: 'lorem ipsum dolor sit amet, consectetur adipiscing elseddo eiusmod There are many variations of passages of lorem Ipsum available,but the majority have suffered alteration.', 
      rate: 4.5, 
      Email:'<EMAIL>',
    }, 
    { 
      name: '<PERSON>', 
      image: 'c2.jpg',
      message: 'There are many variations of passages of Lorem Ipsum available,but the majority have suffered alteration.',
      rate: 3, 
      Email:'<EMAIL>',

    },
    { 
      name: '<PERSON>', 
      image: 'c3.jpg',
      message: 'The majority have suffered alteration',
      rate: 0.5, 
      Email:'<EMAIL>',

    },
    { 
      name: '<PERSON><PERSON><PERSON>', 
      image: 'c4.jpg',
      message: 'lorem ipsum dolor sit amet, consectetur adipiscing elseddo eiusmod There are many variations of passages of lorem Ipsum available,but the majority have suffered alteration.',
      rate: 5, 
      Email:'<EMAIL>',

    },
    { 
      name: 'Emily Haversham', 
      image: 'c5.jpg',
      message: 'lorem ipsum dolor sit amet, consectetur adipiscing elseddo eiusmod There are many variations of passages of lorem Ipsum available,but the majority have suffered alteration.',
      rate: 4.5, 
      Email:'<EMAIL>',

    },
  ];
  


export const Movies=[
    {
        name:'Army Of The Dead',
        desc:'After a zombie outbreak in Las Vegas, a group of mercenaries takes the ultimate gamble by venturing into the quarantine zone for the greatest heist ever.',
        titleImage:'1a.jpg',
        image:'1.jpg',
        Category:'Western',
        language:'English',
        year:'2022',
        time:'3h',
        video:'',
        rate:'3.4',
        reviews:23,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]        
    },
    {
        name:'The Conjuring',
        desc:'After moving into a Rhode Island farmhouse, a family experiences supernatural occurrences and seeks help from a pair of noted paranormal investigators.',
        titleImage:'2a.jpg',
        image:'2.jpg',
        Category:'Thriller',
        language:'English',
        year:'1999',
        time:'4h',
        video:'',
        rate:5,
        reviews:100,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            }

        ]       
    },
    {
        name:'My Favorite Person',
        desc:'Sweet Clark seems like the last person God would tap to fight evil. Hell need his office crush and good pals to help spread the word and save the world.',
        titleImage:'3a.jpg',
        image:'3.jpg',
        Category:'Adventure',
        language:'English',
        year:'2000',
        time:'2h 4min',
        video:'',
        rate:2.5,
        reviews:2,   
        halls: [
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Greenland',
        desc:' A family struggles for survival in the face of a cataclysmic natural disaster.',
        titleImage:'4a.jpg',
        image:'4.jpg',
        Category:'Action',
        language:'English',
        year:'2020',
        time:'1h 50min',
        video :'',
        rate:4.7,
        reviews:30,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Life',
        desc:' A team of scientists aboard the International Space Station discover a rapidly evolving life form that caused extinction on Mars and now threatens all life on Earth.',
        titleImage:'5a.jpg',
        image:'5.jpg',
        Category:'Horror',
        language:'English',
        year:'2017',
        time:'2h 45min',
        video :'',
        rate:4.9,
        reviews:60,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },

            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Legend of the seeker',
        desc:' After the mysterious murder of his father, a sons search for answers begins a momentous fight against tyranny.',
        titleImage:'6a.jpg',
        image:'6.jpg',
        Category:'Romantic',
        language:'English',
        year:'2012',
        time:'3h',
        video :'',
        rate:4.5,
        reviews:34,
        halls: [
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Scarlet Heart',
        desc:' Ha-jin travels 1000 years back in time and lands in the era of Goryeo Dynasty as a young girl named Hae-soo. She, now trapped in another persons body, becomes involved in a power struggle against various vicious contenders to the throne.',
        titleImage:'7a.jpg',
        image:'7.jpg',
        Category:'Fantasy',
        language:'Korean',
        year:'2005',
        time:'2h',
        video :'',
        rate:4.3,
        reviews:20,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            }
        ]       
    },
    {
        name:'J.Edgar',
        desc:'J. Edgar Hoover, powerful head of the F.B.I. for nearly fifty years, looks back on his professional and personal life.',
        titleImage:'8a.jpg',
        image:'8.jpg',
        Category:'Mystery',
        language:'English',
        year:'2011',
        time:'2h 17min',
        video :'',
        rate:3.5,
        reviews:18,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Ted 2',
        desc:'Newlywed couple Ted and Tami-Lynn want to have a baby, but in order to qualify to be a parent, Ted will have to prove hes a person in a court of law.',
        titleImage:'9a.jpg',
        image:'9.jpg',
        Category:'Comedy',
        language:'English',
        year:'2015',
        time:'1h 55min',
        video :'',
        rate:3.2,
        reviews:48,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'White House Down',
        desc:'While on a tour of the White House with his young daughter, a Capitol policeman springs into action to save his child and protect the president from a heavily armed group of paramilitary invaders.',
        titleImage:'10a.jpg',
        image:'10.jpg',
        Category:'Drama',
        language:'English',
        year:'2012',
        time:'2h 11min',
        video :'',
        rate:2.9,
        reviews:17,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },

            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Blitz',
        desc:'The stories of a group of Londoners during the German bombing campaign of the British capital during World War II.',
        titleImage:'11a.jpg',
        image:'11.jpg',
        Category:'Historical',
        language:'English',
        year:'2024',
        time:'2h',
        video :'',
        rate:4.9,
        reviews:23,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            }
        ]       
    },
    {
        name:'WALLE.E',
        desc:'A robot who is responsible for cleaning a waste-covered Earth meets another robot and falls in love with her. Together, they set out on a journey that will alter the fate of mankind.',
        titleImage:'12a.jpg',
        image:'12aa.jpg',
        Category:'Science',
        language:'English',
        year:'2008',
        time:'1h 38min',
        video :'',
        rate:5.0,
        reviews:90,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'Gran Turismo',
        desc:'Based on the unbelievable, inspiring true story of a team of underdogs - a struggling, working-class gamer, a failed former race car driver, and an idealistic motorsport exec - who risk it all to take on the most elite sport in the world.',
        titleImage:'13a.jpg',
        image:'13.jpg',
        Category:'Sports',
        language:'English',
        year:'2023',
        time:'2h 14min',
        video :'',
        rate:4.8,
        reviews:60,
        halls: [

            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },
    {
        name:'The sound of music',
        desc:'A young novice is sent by her convent in 1930s Austria to become a governess to the seven children of a widowed naval officer.',
        titleImage:'14a.jpg',
        image:'14.jpg',
        Category:'Musicals',
        language:'English',
        year:'2001',
        time:'2h 50min',
        video :'',
        rate:4.6,
        reviews:120,
        halls: [
            {
                hallType: 'Standard',
                rows: 6,
                columns: 8,
                aisles: ['A', 'B'],
            },
            {
                hallType: 'VIP',
                rows: 4,
                columns: 6,
                aisles: ['A'],
            },
            {
                hallType: '4K',
                rows: 5,
                columns: 6,
                aisles: ['A', 'B', 'C'],
            }
        ]       
    },

];
