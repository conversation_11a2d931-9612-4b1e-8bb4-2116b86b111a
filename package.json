{"name": "project", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.11", "@tailwindcss/line-clamp": "^0.4.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "aos": "^2.3.4", "axios": "^1.8.4", "express": "^4.21.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "rc-drawer": "^7.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "react-qr-code": "^2.0.15", "react-qr-reader": "^3.0.0-beta-1", "react-responsive": "^10.0.0", "react-router-dom": "^7.0.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "styled-components": "^6.1.13", "swiper": "^11.1.15", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "tailwindcss": "^3.4.15"}}